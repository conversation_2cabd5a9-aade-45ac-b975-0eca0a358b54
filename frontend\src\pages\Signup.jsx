
import { useEffect, useRef, useState } from "react";
import { BiLogoFacebookSquare } from "react-icons/bi";
import { useNavigate } from 'react-router-dom';
import axios from "axios";
import { RxCrossCircled } from "react-icons/rx";
import { FaRegCircleCheck } from "react-icons/fa6";
import LoginFooter from "../components/LoginFooter";
import { AiOutlineReload } from "react-icons/ai";






const Signup = () => {


  const inputRef1 = useRef(null)
  const inputRef2 = useRef(null)
  const inputRef3 = useRef(null)
  const inputRef4 = useRef(null)
  const inputBox = useRef(null)
  const inputBox1 = useRef(null)
  const inputBox3 = useRef(null)
  const inputBox2 = useRef(null)
  const [username, setUsername] = useState('')
  const [pass, setPassword] = useState('')
  const [randomuser, setRandomUser] = useState([])
  const [show, setShow] = useState(false)
  const [available, setAvailable] = useState(true)
  const [email, setEmail] = useState('')
  const [name, setName] = useState('')
  const [valid, setValid] = useState(true);
  const [PassValid, setPassValid] = useState(true);
  const [loading, setLoading] = useState(false)
  const serverUrl = import.meta.env.VITE_SERVER_URL
  const navigate = useNavigate()
  const timerRef = useRef(null);
  const usernameRef = useRef(null);
  const passwordRef = useRef(null);





  useEffect(() => {
    inputBox.current.focus()
    document.title = `Sign up - Instagram`;

  }, [])

  const handleSignup = async (e) => {
    e.preventDefault()
    setLoading(true)
    try {
      let result = await axios.post(`${serverUrl}/api/auth/register`, {
        username,
        email,
        password: pass,
        name
      }, { withCredentials: true })
      console.log(result)
      setLoading(false)
      navigate('/')
      setEmail('')
      setPassword('')
      setName('')
      setUsername('')
    } catch (error) {
      console.log(error);
      setLoading(false)
    }

  }

  useEffect(() => {
    if (pass === '') {
      inputRef2.current.style.fontSize = '';
      inputRef2.current.style.top = '';
      inputBox1.current.style.paddingTop = '';
    }
  }, [pass]);
  useEffect(() => {
    if (username === '') {
      inputRef4.current.style.fontSize = '';
      inputRef4.current.style.top = '';
      inputBox3.current.style.paddingTop = '';
    }
  }, [username]);
  useEffect(() => {
    if (name === '') {
      inputRef3.current.style.fontSize = '';
      inputRef3.current.style.top = '';
      inputBox2.current.style.paddingTop = '';
    }
  }, [name]);
  useEffect(() => {
    if (email === '') {
      inputRef1.current.style.fontSize = '';
      inputRef1.current.style.top = '';
      inputBox.current.style.paddingTop = '';
    }
  }, [email]);

  const handleinput1 = () => {
    inputRef1.current.style.fontSize = '10px';
    inputRef1.current.style.top = '4px';
    inputBox.current.style.paddingTop = '12px';
  }

  const handleinput2 = () => {
    inputRef2.current.style.fontSize = '10px';
    inputRef2.current.style.top = '4px';
    inputBox1.current.style.paddingTop = '12px';
  }

  const handleinput3 = () => {
    inputRef3.current.style.fontSize = '10px';
    inputRef3.current.style.top = '4px';
    inputBox2.current.style.paddingTop = '12px';
  }

  const handleinput4 = () => {
    inputRef4.current.style.fontSize = '10px';
    inputRef4.current.style.top = '4px';
    inputBox3.current.style.paddingTop = '12px';
  }

  const handleblur = () => {
    if (inputBox.current.value === '') {
      inputRef1.current.style.fontSize = '';
      inputRef1.current.style.top = '';
      inputBox.current.style.paddingTop = '';
    }
  }

  const handleblur2 = () => {
    if (inputBox1.current.value === '') {
      inputRef2.current.style.fontSize = '';
      inputRef2.current.style.top = '';
      inputBox1.current.style.paddingTop = '';
    }
  }

  const handleblur3 = () => {
    if (inputBox2.current.value === '') {
      inputRef3.current.style.fontSize = '';
      inputRef3.current.style.top = '';
      inputBox2.current.style.paddingTop = '';
    }
  }

  const handleblur4 = () => {
    if (inputBox3.current.value === '') {
      inputRef4.current.style.fontSize = '';
      inputRef4.current.style.top = '';
      inputBox3.current.style.paddingTop = '';
    }
  }
  const handleShow = () => {
    setShow(!show)
    setTimeout(() => {
      setShow(false)
    }, 1000)
  }

  const validateEmail = (value) => {
    setEmail(value);
    clearTimeout(timerRef.current);

    if (value === '') {
      setValid(true)
    } else {
      timerRef.current = setTimeout(() => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        setValid(emailRegex.test(value));
      }, 1600);
    }
  };

  const handlePassword = (value) => {
    setPassword(value);
    clearTimeout(passwordRef.current);


    if (value === '') {
      setPassValid(true)
    } else {
      passwordRef.current = setTimeout(() => {
        const passRegex = /^(?=.*[a-zA-Z])(?=.*\d)(?=.*[\W_]).{6,}$/;
        setPassValid(passRegex.test(value));
      }, 1000);
    }
  };


  const handleUsername = async (e) => {
    const value = e.target.value.toLowerCase().slice(0, 15);
    setUsername(value);
    clearTimeout(usernameRef.current);
    if (!username) {
      setAvailable(true)
    }
    if (value === '') {
      setAvailable(true)
    }

    handleSuggestions(value)

    handleName(value)

  };

  const handleSuggestions = (value) => {
    const threeDigit = Math.floor(100 + Math.random() * 900);
    const fourDigit = Math.floor(1000 + Math.random() * 9000);

    let first = value + threeDigit;
    let second = value + fourDigit;



    setRandomUser([first, second])
  }


  const handleName = (value) => {

    usernameRef.current = setTimeout(async () => {
      if (value.length > 0) {
        try {
          let useravailable = await axios.post(`${serverUrl}/api/auth/checkusername`, { username: value });
          console.log(useravailable);
          if (useravailable.status === 400) {
            setAvailable(false)
          } else {
            setAvailable(true)
          }

        } catch (error) {
          console.log(error);
          setAvailable(false)
        }

      }
    }, 700);

  }


  useEffect(() => {
    setAvailable(true)
  }, [])



  useEffect(() => {
    const fetchData = async () => {
      const value = username.toLowerCase().slice(0, 15);

      try {
        let useravailable = await axios.post(`${serverUrl}/api/auth/checkusername`, { username: value });
        console.log(useravailable);
        if (useravailable.status === 400) {
          setAvailable(false)
        } else {
          setAvailable(true)
        }

      } catch (error) {
        console.log(error);
        setAvailable(false)
      }
    }

    fetchData()


  }, [username, serverUrl]);














  return (
    /* Main page container - Full screen background with centered content */
    <div className='w-full min-h-screen flex flex-col justify-start items-center bg-black pt-2 md:pt-4'>

      {/* Main signup form container - Contains Instagram logo, form fields, and signup button */}
      <div className='flex flex-col justify-center items-center md:border-1 border-[#363636] px-auto p-10 pt-10'>
        <h1 className='heading text-5xl tracking-tight font-medium text-gray-100 mb-5 pt-2'>Instagram</h1>

        <p className='text-[#c5c1bcc4] text-[16px] font-semibold mb-2 text-center leading-5'>Sign up to see photos and videos<br /> from your friends.</p>
        <button className="flex items-end px-12 py-[6px] rounded-lg gap-2 bg-[#0095f6] text-[#fffafd] font-semibold text-sm mt-2 transition-all duration-200 ease-in-out hover:bg-[#2d6dd6b7] active:scale-95 cursor-pointer"><BiLogoFacebookSquare size={22} /><p >Log in with Facebook</p></button>

        {/* OR divider section - Visual separator between Facebook login and form fields */}
        <form className='flex items-center flex-col justify-center '>
          <div className='flex items-center justify-center gap-4 mt-4'>
            <hr className='w-[108px] h-[2px] bg-[#55555574] ' />
            <p className='text-[#ffffffa5] text-[13px] '>OR</p>
            <hr className='w-[108px] h-[2px] bg-[#55555574]' />
          </div>

          {/* Form input fields container - Contains all signup form inputs (email, password, name, username) */}
          <div className="flex flex-col pt-4 gap-2">

            {/* Email input field with validation */}
            <div onKeyDown={handleinput1} className='relative'>
              <input required ref={inputBox} onBlur={handleblur} type="email" value={email} onChange={(e) => validateEmail(e.target.value)} className={`${!valid ? 'border-[#ff3040] border-1' : ''} w-[270px] h-[36px]  pl-3 border border-[#555555] outline-none text-xs text-gray-300 bg-[#121212] rounded-[3px]  `} />
              <div ref={inputRef1} onClick={() => { inputBox.current.focus() }} className='absolute top-[9px] left-3 text-xs z-1  transition-all duration-300 ease-in-out'>
                <div className='flex gap-41 justify-center '>
                  <p className='text-[#b0abab]'>Email Address</p>
                  {!valid && <RxCrossCircled size={26} className="text-[#ff3040] pt-[2px]" />}
                </div>
              </div>
              {(valid && email.length > 4) && <div className='absolute flex items-center justify-center gap-2 top-2 right-3 '>
                <FaRegCircleCheck size={22} className="text-[#909090]" />
              </div>}
              {!valid && <p className="text-[#ff3040]  text-xs pt-1 pb-2 px-auto md:px-2">Enter a valid email address.</p>}
            </div>

            {/* Password input field with show/hide toggle and validation */}
            <div onKeyDown={handleinput2} className='relative'>
              <input required ref={inputBox1} onBlur={handleblur2} type={show ? 'text' : 'password'} value={pass} onChange={e => handlePassword(e.target.value)} className={`${!PassValid ? 'border-[#ff3040] border-1' : ''} w-[270px] h-[36px] border pl-3 border-[#555555] outline-none text-xs text-white bg-[#121212] rounded-[3px]`} />
              <div ref={inputRef2} onClick={() => { inputBox1.current.focus() }} className='absolute top-[9px] left-3 text-xs z-1 transition-all duration-300 ease-in-out'>
                <p className='text-[#b0abab]'>Password</p>
              </div>
              {pass.length > 0 && <div className={`absolute flex items-center justify-center gap-2 right-3 ${!PassValid ? 'top-1' : 'top-2 '}`}>
                {(pass.length >= 5 && PassValid) ? <FaRegCircleCheck size={22} className="text-[#909090]" /> : <RxCrossCircled size={26} className="text-[#ff3040] pt-[2px]" />}
                <p onClick={() => { handleShow() }} className={` cursor-pointer text-white font-semibold text-sm transition-all duration-300 ease-in-out hover:text-[#919191]`}>{show ? 'Hide' : 'Show'}</p>
              </div>}
            </div>
            {!PassValid && <p className="text-[#ff3040] text-xs pb-2 px-2">This password is too easy to guess. Please create<br /> a new one.</p>}

            {/* Full name input field */}
            <div onKeyDown={handleinput3} className='relative'>
              <input ref={inputBox2} onBlur={handleblur3} type="text" value={name} onChange={(e) => setName(e.target.value)} className='w-[270px] h-[36px]  pl-3 border border-[#555555] outline-none text-xs text-white bg-[#121212] rounded-[3px] ' />
              <div ref={inputRef3} onClick={() => { inputBox2.current.focus() }} className='absolute top-[9px] left-3 text-xs z-1 transition-all duration-300 ease-in-out'>
                <p className='text-[#b0abab]'>Full Name</p>
              </div>
              {name.length >= 3 && <div className='absolute flex items-center justify-center gap-2 top-2 right-3 '>
                <FaRegCircleCheck size={22} className="text-[#909090]" />
              </div>}
            </div>

            {/* Username input field */}
            <div onKeyDown={handleinput4} className='relative'>
              <input required ref={inputBox3} onBlur={handleblur4} type="text" value={username} onChange={handleUsername} className={`${!available ? 'border-[#ff3040] border-1' : ''} w-[270px] h-[36px]  pl-3 border border-[#555555] outline-none text-xs text-white bg-[#121212] rounded-[3px]`} />
              <div ref={inputRef4} onClick={() => { inputBox3.current.focus() }} className={`absolute top-[9px] left-3 text-xs z-1 transition-all duration-300 ease-in-out`}>
                <p className='text-[#b0abab]'>Username</p>
              </div>
              <div className={`absolute flex items-center justify-center gap-1 right-3 ${!available ? 'top-1' : 'top-2 '}`}>
                {(available && username.length >= 3) && <FaRegCircleCheck size={22} className="text-[#909090]" />}
                {(!available && username.length >= 3) && <RxCrossCircled size={26} className="text-[#ff3040] pt-[2px]" />}
                {(!available && username.length >= 3) && <AiOutlineReload onClick={() => { handleSuggestions(username) }} size={26} className="text-[#4492d8] cursor-pointer active:scale-95 pt-[2px]" />}

              </div>
            </div>

            {!available && <p className="text-[#cf2f3d]  text-xs pb-1 px-2">This username isn't available. Please try another.</p>}
            <div className={`flex  justify-start items-center  ${username.length <= 8 ? '' : 'gap-3 items-start flex-col'}`}>
              {!available && randomuser.map((item, index) => (
                <div key={index} className={`flex ${item.length <= 9 ? 'gap-2' : 'gap-0 flex-col'} pb-1`}>
                  <p key={index} className="text-[#ffffff]  text-xs py-1 px-2">{index === 0 ? 'Try:  ' : ''}<span onClick={() => { setUsername(item); }} className={`${index === 0 && username.length <= 8 ? 'ml-10' : ''} text-[white] ml-2 rounded-lg bg-[#474545] px-4 py-2 text-md font-semibold cursor-pointer ${(username.length > 8 && index !== 0) ? 'ml-8' : ''}`}>{item}</span></p>
                </div>
              ))}
            </div>
          </div>

          {/* Terms and privacy policy text section */}
          <div>
            <p className='text-[#ffffffa5] text-xs mt-1 text-center'>People who use our service may have uploaded<br /> your contact information to Instagram. <span className='text-[#708dff] cursor-pointer'>Learn<br /> More</span></p>
            <p className='text-[#ffffffa5] text-xs mt-4 text-center'>By signing up, you agree to our <span className='text-[#708dff] cursor-pointer'>Terms</span> , <span className='text-[#708dff] cursor-pointer'> Privacy<br /> Policy</span > and <span className='text-[#708dff] cursor-pointer'>Cookies Policy</span> .</p>
          </div>
          <button disabled={username === '' || pass === '' || email === '' || !PassValid || !valid || !available} onClick={handleSignup} className={`${username === '' || email === '' || pass.length < 5 || !PassValid || !valid || !available ? 'bg-[#0069ad] text-[#aaafb3]' : 'bg-[#4a8df9] hover:bg-[#4a5ef9b7] text-white active:scale-95'
            } w-[270px] h-[34px] cursor-pointer rounded-lg font-semibold text-sm mt-4 transition-all duration-200 flex items-center justify-center`}
          >
            {loading ? (
              <div className="w-4 h-4 border-t-1 border-b-1 border-white rounded-full animate-spin "></div>
            ) : (
              'Sign up'
            )}
          </button>
        </form>
      </div>

      {/* Login redirect section - Link to login page for existing users */}
      <div className='md:border-1 border-[#363636] flex flex-col items-center justify-center py-6 bg-black w-full md:w-fit md:px-[121px] mt-[10px] '>
        <p onClick={() => { navigate('/login') }} className='text-[#ffffffe9] text-sm text-center leading-3'>Have an account? <br /><span className='text-[#007fce] cursor-pointer font-semibold text-sm'>Log in</span> </p>
      </div>
      <LoginFooter page="signUp" />
    </div>
  )
}

export default Signup
